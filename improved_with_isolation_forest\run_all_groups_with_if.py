#!/usr/bin/env python3
"""
批量运行所有组别的ACGAN-FG孤立森林集成训练
依次运行A、B、C、D、E组，每组进行基线和孤立森林过滤两种实验
"""

import os
import sys
import subprocess
import time
import json
from datetime import datetime
import logging

def setup_logging():
    """设置日志系统"""
    log_dir = "logs/isolation_forest/batch_training"
    os.makedirs(log_dir, exist_ok=True)
    
    current_time = datetime.now().strftime("%Y%m%d-%H%M%S")
    log_file = os.path.join(log_dir, f"batch_training_if_{current_time}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger('BatchTrainingIF'), log_file

def run_single_experiment(group, use_if=True, epochs=200, batch_size=120, contamination=0.1):
    """运行单个实验"""
    logger = logging.getLogger('BatchTrainingIF')
    
    experiment_type = "with_IF" if use_if else "baseline"
    logger.info(f"🚀 开始训练 Group {group} ({experiment_type}) - {epochs} epochs")
    start_time = time.time()
    
    # 构建命令
    cmd = [
        sys.executable,
        "improved_with_isolation_forest/run_enhanced_baseline_with_if.py",
        "--group", group,
        "--epochs", str(epochs),
        "--batch_size", str(batch_size),
        "--contamination", str(contamination)
    ]
    
    if use_if:
        cmd.append("--use_if")
    
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 运行训练
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # 实时读取输出
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                output_lines.append(output)
                logger.info(f"[Group {group} {experiment_type}] {output.strip()}")

        return_code = process.poll()
        full_output = ''.join(output_lines)
        
        end_time = time.time()
        duration = end_time - start_time

        if return_code == 0:
            logger.info(f"✅ Group {group} ({experiment_type}) 训练成功完成!")
            logger.info(f"⏱️  训练时间: {duration/3600:.2f} 小时")
            
            # 提取最佳准确率
            best_accuracy = extract_best_accuracy(full_output)
            if best_accuracy:
                logger.info(f"📊 Group {group} ({experiment_type}) 最佳准确率: {best_accuracy:.4f}")

            return {
                'group': group,
                'experiment_type': experiment_type,
                'use_isolation_forest': use_if,
                'contamination': contamination,
                'status': 'success',
                'duration_hours': duration/3600,
                'best_accuracy': best_accuracy,
                'stdout': full_output[-1000:],
                'stderr': ""
            }
        else:
            logger.error(f"❌ Group {group} ({experiment_type}) 训练失败!")
            logger.error(f"返回码: {return_code}")

            return {
                'group': group,
                'experiment_type': experiment_type,
                'use_isolation_forest': use_if,
                'contamination': contamination,
                'status': 'failed',
                'duration_hours': duration/3600,
                'return_code': return_code,
                'stdout': full_output[-1000:],
                'stderr': ""
            }
            
    except Exception as e:
        logger.error(f"💥 Group {group} ({experiment_type}) 训练出现异常: {e}")
        return {
            'group': group,
            'experiment_type': experiment_type,
            'use_isolation_forest': use_if,
            'contamination': contamination,
            'status': 'error',
            'error': str(e),
            'duration_hours': 0
        }

def extract_best_accuracy(stdout_text):
    """从训练输出中提取最佳准确率"""
    try:
        lines = stdout_text.split('\n')
        best_acc = 0.0

        for line in lines:
            if 'Overall Best Accuracy:' in line:
                parts = line.split('Overall Best Accuracy:')
                if len(parts) > 1:
                    acc_str = parts[1].strip()
                    acc = float(acc_str)
                    best_acc = max(best_acc, acc)

        return best_acc if best_acc > 0 else None
    except Exception as e:
        return None

def save_results(results, log_file):
    """保存训练结果到JSON文件"""
    results_file = log_file.replace('.log', '_results.json')
    
    summary = {
        'timestamp': datetime.now().isoformat(),
        'total_experiments': len(results),
        'successful_experiments': len([r for r in results if r['status'] == 'success']),
        'failed_experiments': len([r for r in results if r['status'] != 'success']),
        'results': results
    }
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    return results_file

def print_summary(results):
    """打印训练结果总结"""
    logger = logging.getLogger('BatchTrainingIF')
    
    logger.info("\n" + "="*80)
    logger.info("🎯 孤立森林集成实验结果总结")
    logger.info("="*80)
    
    total_time = sum(r.get('duration_hours', 0) for r in results)
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] != 'success']
    
    logger.info(f"📊 总体统计:")
    logger.info(f"   总训练时间: {total_time:.2f} 小时")
    logger.info(f"   成功实验: {len(successful)}/{len(results)}")
    logger.info(f"   失败实验: {len(failed)}/{len(results)}")
    
    # 按组别和实验类型分组显示结果
    groups = ['A', 'B', 'C', 'D', 'E']
    experiment_types = ['baseline', 'with_IF']
    
    logger.info(f"\n📋 详细结果:")
    logger.info(f"{'组别':<6} {'实验类型':<12} {'状态':<8} {'准确率':<10} {'时间(h)':<8}")
    logger.info("-" * 50)
    
    for group in groups:
        for exp_type in experiment_types:
            result = next((r for r in results if r['group'] == group and r['experiment_type'] == exp_type), None)
            if result:
                status = "✅成功" if result['status'] == 'success' else "❌失败"
                acc = f"{result.get('best_accuracy', 0):.4f}" if result.get('best_accuracy') else "N/A"
                duration = f"{result.get('duration_hours', 0):.2f}"
                logger.info(f"{group:<6} {exp_type:<12} {status:<8} {acc:<10} {duration:<8}")
    
    # 对比分析
    logger.info(f"\n🔍 孤立森林效果分析:")
    for group in groups:
        baseline_result = next((r for r in successful if r['group'] == group and r['experiment_type'] == 'baseline'), None)
        if_result = next((r for r in successful if r['group'] == group and r['experiment_type'] == 'with_IF'), None)
        
        if baseline_result and if_result:
            baseline_acc = baseline_result.get('best_accuracy', 0)
            if_acc = if_result.get('best_accuracy', 0)
            improvement = if_acc - baseline_acc
            improvement_pct = (improvement / baseline_acc * 100) if baseline_acc > 0 else 0
            
            status = "📈提升" if improvement > 0 else "📉下降" if improvement < 0 else "➡️持平"
            logger.info(f"   Group {group}: {baseline_acc:.4f} -> {if_acc:.4f} "
                       f"({improvement:+.4f}, {improvement_pct:+.1f}%) {status}")

def main():
    """主函数"""
    print("🚀 开始批量训练所有组别的ACGAN-FG孤立森林集成实验")
    print("="*80)
    print("📋 实验计划:")
    print("   - 组别: A, B, C, D, E")
    print("   - 实验类型: 基线 + 孤立森林过滤")
    print("   - 每组轮次: 200 epochs")
    print("   - 批次大小: 120")
    print("   - 异常比例: 0.1 (10%)")
    print("   - 预计总时间: 15-20小时")
    print("="*80)
    
    # 设置日志
    logger, log_file = setup_logging()
    logger.info("批量训练开始")
    
    # 确认工作目录
    if not os.path.exists("improved_with_isolation_forest/run_enhanced_baseline_with_if.py"):
        logger.error("❌ 找不到训练脚本!")
        logger.error("请确保在正确的目录下运行此脚本")
        return False
    
    # 实验配置
    groups = ['A', 'B', 'C', 'D', 'E']
    epochs = 200
    batch_size = 120
    contamination = 0.1
    
    # 开始批量训练
    results = []
    total_start_time = time.time()
    
    experiment_count = 0
    total_experiments = len(groups) * 2  # 每组2个实验（基线+孤立森林）
    
    for group in groups:
        # 先运行基线实验
        experiment_count += 1
        logger.info(f"\n{'='*60}")
        logger.info(f"📍 进度: {experiment_count}/{total_experiments} - Group {group} 基线实验")
        logger.info(f"{'='*60}")
        
        baseline_result = run_single_experiment(group, use_if=False, epochs=epochs, 
                                              batch_size=batch_size, contamination=contamination)
        results.append(baseline_result)
        
        # 再运行孤立森林实验
        experiment_count += 1
        logger.info(f"\n{'='*60}")
        logger.info(f"📍 进度: {experiment_count}/{total_experiments} - Group {group} 孤立森林实验")
        logger.info(f"{'='*60}")
        
        if_result = run_single_experiment(group, use_if=True, epochs=epochs, 
                                        batch_size=batch_size, contamination=contamination)
        results.append(if_result)
        
        # 显示当前组别的对比结果
        if baseline_result['status'] == 'success' and if_result['status'] == 'success':
            baseline_acc = baseline_result.get('best_accuracy', 0)
            if_acc = if_result.get('best_accuracy', 0)
            improvement = if_acc - baseline_acc
            logger.info(f"🔍 Group {group} 对比: 基线 {baseline_acc:.4f} vs 孤立森林 {if_acc:.4f} "
                       f"(改进: {improvement:+.4f})")
    
    # 计算总时间
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    logger.info(f"\n🎉 批量训练全部完成!")
    logger.info(f"⏱️  总耗时: {total_duration/3600:.2f} 小时")
    
    # 保存结果
    results_file = save_results(results, log_file)
    logger.info(f"💾 结果已保存到: {results_file}")
    
    # 打印总结
    print_summary(results)
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断训练")
        exit(1)
    except Exception as e:
        print(f"\n💥 批量训练出现异常: {e}")
        exit(1)
