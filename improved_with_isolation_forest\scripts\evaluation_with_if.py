"""
集成孤立森林过滤机制的评估模块
基于原始evaluation.py，添加了生成样本质量过滤功能
"""

from keras.models import load_model
import tensorflow as tf
import numpy as np
import random
from sklearn import preprocessing
from sklearn.svm import SVR, SVC, LinearSVC
from sklearn.metrics import confusion_matrix, accuracy_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from tensorflow.keras import regularizers
from tensorflow.keras.layers import Input, Dense, Reshape, Flatten, Dropout, Concatenate, multiply
from tensorflow.keras.layers import UpSampling2D, Conv2D, Conv1D, MaxPooling1D, UpSampling1D
from tensorflow.keras.layers import BatchNormalization, Activation, ZeroPadding2D, Embedding, concatenate
from tensorflow.keras.regularizers import l2
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LeakyReLU
import logging

# 导入孤立森林过滤器
try:
    from .isolation_forest_filter import IsolationForestFilter, log_filter_metrics_to_tensorboard
except ImportError:
    from isolation_forest_filter import IsolationForestFilter, log_filter_metrics_to_tensorboard

def scalar_stand(Train_X, Test_X):
    """用训练集标准差标准化训练集以及测试集"""
    scalar_train = preprocessing.StandardScaler().fit(Train_X)
    Train_X = scalar_train.transform(Train_X)
    Test_X = scalar_train.transform(Test_X)
    return Train_X, Test_X

def feature_generation_and_diagnosis_with_if(add_quantity, test_x, test_y, autoencoder, generator, classifier, 
                                           test_class_indices, use_isolation_forest=True, 
                                           contamination=0.1, tensorboard_writer=None, step=0):
    """
    带孤立森林过滤的特征生成和诊断函数
    
    Args:
        add_quantity: 每个类别生成的样本数量
        test_x: 测试数据
        test_y: 测试标签
        autoencoder: 自编码器模型
        generator: 生成器模型
        classifier: 分类器模型
        test_class_indices: 测试类别索引
        use_isolation_forest: 是否使用孤立森林过滤
        contamination: 孤立森林异常比例
        tensorboard_writer: TensorBoard写入器
        step: 当前步数
        
    Returns:
        四个分类器的准确率: (lsvm_acc, nrf_acc, pnb_acc, mlp_acc)
    """
    
    logger = logging.getLogger('EvaluationWithIF')
    logger.info(f"开始特征生成和诊断，使用孤立森林: {use_isolation_forest}")
    logger.info(f"测试类别: {test_class_indices}")

    # 确定训练中未见的类别
    all_classes = np.arange(1, 16)
    seen_classes = np.setdiff1d(all_classes, test_class_indices)
    logger.info(f"训练类别: {seen_classes}")
    
    Labels_train = []
    Labels_test = []
    Generated_feature = []

    samples_per_class = len(test_x) // len(test_class_indices)
    
    # 初始化孤立森林过滤器
    if use_isolation_forest:
        isolation_filter = IsolationForestFilter(contamination=contamination)
        logger.info(f"初始化孤立森林过滤器，异常比例: {contamination}")
    
    # 为每个测试类别生成特征
    for i, class_idx in enumerate(test_class_indices):
        
        # 动态获取属性向量
        start_idx = i * samples_per_class
        end_idx = (i + 1) * samples_per_class
        class_test_x = test_x[start_idx:end_idx]
        class_test_y = test_y[start_idx:end_idx]
        
        # 获取真实特征（用于训练孤立森林）
        real_features, _ = autoencoder(class_test_x)
        
        # 训练孤立森林过滤器（使用真实特征作为参考）
        if use_isolation_forest:
            isolation_filter.fit_filter(real_features.numpy())
        
        # 生成指定数量的样本
        logger.info(f"为类别 {class_idx} 生成 {add_quantity} 个样本")
        
        # 使用真实样本的属性向量作为生成条件
        if len(class_test_y) > 0:
            # 随机选择一个真实样本的属性作为模板
            template_attribute = class_test_y[0:1]  # 取第一个样本的属性
            
            # 生成噪声和属性
            noise_shape = (add_quantity, 50, 1)
            noise = tf.random.normal(shape=noise_shape)
            
            # 复制属性向量
            repeated_attributes = np.tile(template_attribute, (add_quantity, 1))
            
            # 生成特征
            generated_features = generator([noise, repeated_attributes])
            
            # 应用孤立森林过滤
            if use_isolation_forest:
                original_count = generated_features.shape[0]
                filtered_features, _, filter_info = isolation_filter.filter_samples(
                    generated_features.numpy(),
                    min_samples_ratio=0.2  # 至少保留20%的样本
                )
                
                # 记录过滤信息到日志
                logger.info(f"类别 {class_idx} 过滤结果: {original_count} -> {filtered_features.shape[0]} "
                           f"(移除率: {filter_info['removal_rate']:.2%})")
                
                # 记录到TensorBoard
                if tensorboard_writer is not None:
                    log_filter_metrics_to_tensorboard(tensorboard_writer, filter_info, step)
                
                # 使用过滤后的特征
                final_features = filtered_features
            else:
                final_features = generated_features.numpy()
            
            # 添加到训练集
            Generated_feature.append(final_features)
            
            # 创建对应的标签
            class_labels = np.full(final_features.shape[0], class_idx)
            Labels_train.extend(class_labels)
        
        # 添加测试标签
        test_labels = np.full(len(class_test_x), class_idx)
        Labels_test.extend(test_labels)
    
    # 合并所有生成的特征
    if Generated_feature:
        Generated_feature = np.vstack(Generated_feature)
        logger.info(f"总生成特征数量: {Generated_feature.shape[0]}")
    else:
        logger.warning("没有生成任何特征!")
        return 0.0, 0.0, 0.0, 0.0

    # 检查是否有足够的样本进行训练
    if Generated_feature.shape[0] == 0:
        logger.warning("过滤后没有剩余样本，返回零准确率")
        return 0.0, 0.0, 0.0, 0.0
    
    # 获取测试特征
    test_features, _ = autoencoder(test_x)
    test_features = test_features.numpy()
    
    # 准备训练和测试数据
    Train_X = Generated_feature
    Train_Y = np.array(Labels_train)
    Test_X = test_features
    Test_Y = np.array(Labels_test)
    
    # 标准化
    Train_X, Test_X = scalar_stand(Train_X, Test_X)
    
    logger.info(f"训练数据形状: {Train_X.shape}, 测试数据形状: {Test_X.shape}")
    
    # 训练和评估四种分类器
    classifiers = {
        'LSVM': LinearSVC(random_state=42, max_iter=2000),
        'NRF': RandomForestClassifier(n_estimators=100, random_state=42),
        'PNB': GaussianNB(),
        'MLP': MLPClassifier(hidden_layer_sizes=(100,), max_iter=500, random_state=42)
    }
    
    accuracies = {}
    
    for name, clf in classifiers.items():
        try:
            # 训练分类器
            clf.fit(Train_X, Train_Y)
            
            # 预测
            predictions = clf.predict(Test_X)
            
            # 计算准确率
            accuracy = accuracy_score(Test_Y, predictions)
            accuracies[name] = accuracy
            
            logger.info(f"{name} 准确率: {accuracy:.4f}")
            
        except Exception as e:
            logger.error(f"{name} 分类器训练失败: {e}")
            accuracies[name] = 0.0
    
    # 输出孤立森林统计信息
    if use_isolation_forest:
        stats = isolation_filter.get_statistics()
        logger.info(f"孤立森林统计: 总处理样本 {stats['total_samples_processed']}, "
                   f"总移除样本 {stats['total_samples_removed']}, "
                   f"平均移除率 {stats['removal_rate']:.2%}")
    
    return accuracies.get('LSVM', 0.0), accuracies.get('NRF', 0.0), \
           accuracies.get('PNB', 0.0), accuracies.get('MLP', 0.0)

# 保持向后兼容的原始函数
def feature_generation_and_diagnosis(add_quantity, test_x, test_y, autoencoder, generator, classifier, test_class_indices):
    """
    原始的特征生成和诊断函数（不使用孤立森林过滤）
    保持与原始代码的兼容性
    """
    return feature_generation_and_diagnosis_with_if(
        add_quantity, test_x, test_y, autoencoder, generator, classifier, 
        test_class_indices, use_isolation_forest=False
    )
