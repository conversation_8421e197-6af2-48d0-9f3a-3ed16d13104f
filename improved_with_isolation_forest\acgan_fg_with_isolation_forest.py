"""
集成孤立森林过滤机制的ACGAN-FG模型
基于原始acgan_fg_yuanma.py，添加了生成样本质量过滤功能

主要改进：
1. 保持原有架构不变
2. 在评估阶段集成孤立森林过滤
3. 添加过滤效果监控和日志记录
"""

import numpy as np
from tensorflow.keras.layers import Layer
from tensorflow.keras import backend as K
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense, LeakyReLU, ReLU,LayerNormalization,BatchNormalization,Conv1D,Reshape,concatenate,Flatten, Dropout, Concatenate,multiply
from tensorflow.keras.models import Sequential, Model
import datetime
import sys
import os

# 添加scripts目录到路径
sys.path.append('scripts')
sys.path.append('improved_with_isolation_forest/scripts')

from tensorflow.keras.losses import mean_squared_error

class RandomWeightedAverage(Concatenate):
    """Provides a (random) weighted average between real and generated samples"""
    def call(self, inputs):
        batch_size = tf.shape(inputs[0])[0]
        alpha = K.random_uniform((batch_size, 1))
        return (alpha * inputs[0]) + ((1 - alpha) * inputs[1])
    
class Zero_shot_with_IF():
    """
    集成孤立森林过滤机制的Zero-shot学习模型
    基于原始Zero_shot类，添加了样本质量过滤功能
    """
    def __init__(self, use_isolation_forest=True, contamination=0.1):
        # 基本参数（与原始模型保持一致）
        self.data_lenth=52
        self.sample_shape=(self.data_lenth,)
        
        self.feature_dim=256
        self.feature_shape=(256,)
        self.num_classes=15
        self.latent_dim = 50
        self.noise_shape=(self.latent_dim,1)
        self.n_critic = 1  #randomly select from (1,5) 
        self.LAMBDA_GP=10  #randomly select from (5,15) 
        self.num_blocks=3  #randomly select from (1,6) 
        self.crl = False   #self.crl =True

        self.lambda_cla = tf.constant(10.0, dtype=tf.float32)
        self.lambda_cms = tf.constant(10.0, dtype=tf.float32)
        self.lambda_crl = tf.constant(0.01, dtype=tf.float32)
        
        self.bound = False #self.bound =True
        self.mi_weight = 0.001 #randomly select from (0,1)
        self.mi_bound = 100 #randomly select from (10,100)
        
        # 优化器
        self.autoencoder_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        self.d_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        self.g_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        self.c_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        self.m_optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
        
        # 孤立森林相关参数
        self.use_isolation_forest = use_isolation_forest
        self.contamination = contamination
        
        # 构建模型
        self.autoencoder= self.build_autoencoder()
        self.d = self.build_discriminator()
        self.g = self.build_generator()
        self.c= self.build_classifier()
        self.m= self.build_comparator()

        # 设置训练状态
        self.autoencoder.trainable = False
        self.c.trainable = False
        self.m.trainable = True
        self.d.trainable = False
        self.g.trainable = False

        # 构建比较器模型
        sample_1= Input(shape=self.sample_shape)
        feature_1, output_sample_1=self.autoencoder(sample_1)

        sample_2= Input(shape=self.sample_shape)
        feature_2, output_sample_1=self.autoencoder(sample_2)

        predicted_similarity=self.m([feature_1,feature_2])

        self.m_model=Model(inputs=[sample_1,sample_2], outputs=[predicted_similarity])
        self.m_model.compile(loss=['binary_crossentropy'], optimizer=self.m_optimizer)

    def build_autoencoder(self):
        """构建自编码器（与原始版本相同）"""
        sample = Input(shape=self.sample_shape)     
        a0=sample

        # Encoder
        a1=Dense(100)(a0)
        a1=LeakyReLU(alpha=0.2)(a1)
        a1=LayerNormalization()(a1)

        a2=Dense(200)(a1)
        a2=LeakyReLU(alpha=0.2)(a2)
        a2=LayerNormalization()(a2)

        a3=Dense(256)(a2)
        a3=LeakyReLU(alpha=0.2)(a3)
        a3=LayerNormalization()(a3)

        feature=a3

        # Decoder
        a4=Dense(200)(feature)
        a4=LeakyReLU(alpha=0.2)(a4)
        a4=LayerNormalization()(a4)

        a5=Dense(100)(a4)
        a5=LeakyReLU(alpha=0.2)(a5)
        a5=LayerNormalization()(a5)

        a6=Dense(52)(a5)
        a6=LeakyReLU(alpha=0.2)(a6)
        a6=LayerNormalization()(a6)

        output_sample=a6

        # Autoencoder Model
        autoencoder = Model(sample,[feature, output_sample])
        return autoencoder    

    def build_discriminator(self):
        """构建判别器（与原始版本相同）"""
        sample_input = Input(shape=self.feature_shape)
        reshaped_sample = Reshape((256, 1))(sample_input)

        attribute = Input(shape=(20,), dtype='float32')
        reshape_attribute= Reshape((20,1))(attribute)

        concatenated = concatenate([reshaped_sample, reshape_attribute], axis=1)

        d0=Flatten()(concatenated)

        d1=Dense(200)(d0)
        d1=LeakyReLU(alpha=0.2)(d1)
        d1=LayerNormalization()(d1)

        d2=Dense(100)(d1)
        d2=LeakyReLU(alpha=0.2)(d2)
        d2=LayerNormalization()(d2)

        validity = Dense(1)(d2)

        return Model([sample_input,attribute],validity)

    def build_generator(self):
        """构建生成器（与原始版本相同）"""
        noise = Input(shape=self.noise_shape)
        
        attribute = Input(shape=(20,), dtype='float32')
        reshape_attribute= Reshape((20,1))(attribute)

        concatenated = concatenate([noise, reshape_attribute], axis=1)

        g0=Flatten()(concatenated)

        g1=Dense(100)(g0)
        g1=LeakyReLU(alpha=0.2)(g1)
        g1=LayerNormalization()(g1)

        g2=Dense(200)(g1)
        g2=LeakyReLU(alpha=0.2)(g2)
        g2=LayerNormalization()(g2)

        g3=Dense(256)(g2)
        g3=LeakyReLU(alpha=0.2)(g3)
        
        generated_feature=g3
        generated_feature=BatchNormalization()(generated_feature)

        return Model([noise,attribute],generated_feature)

    def build_classifier(self):
        """构建分类器（与原始版本相同）"""
        sample = Input(shape=self.feature_shape)

        c0=sample

        c1=Dense(100)(c0)
        c1=LeakyReLU(alpha=0.2)(c1)

        c2=Dense(50)(c1)
        c2=LeakyReLU(alpha=0.2)(c2)

        hidden_ouput=c2
        c3 = Dense(20,activation="sigmoid")(c2)

        predict_attribute=c3
        
        return Model(sample,[hidden_ouput,predict_attribute])

    def build_comparator(self):
        """构建比较器（与原始版本相同）"""
        def conv_block(x, filters, stride, kernel_size=3):
            x = Conv1D(filters, kernel_size=kernel_size, strides=stride, padding='same')(x)
            x = BatchNormalization()(x)
            x = ReLU()(x)
            return x

        sample_1 = Input(shape=self.feature_shape)
        sample_2 = Input(shape=self.feature_shape)

        s1=Reshape((256,1))(sample_1)
        s2=Reshape((256,1))(sample_2)

        concatenated=concatenate([s1, s2], axis=-1)

        c0=concatenated
        c1=conv_block(c0,filters=16,stride=2)
        c2=conv_block(c1,filters=32,stride=2)
        c3=conv_block(c2,filters=64,stride=1)

        c4=Flatten()(c3)

        c5=Dense(1500)(c4)
        c5=LeakyReLU(alpha=0.2)(c5)
        c5=LayerNormalization()(c5)

        c6=Dense(100)(c5)
        c6=LeakyReLU(alpha=0.2)(c6)
        c6=LayerNormalization()(c6)

        c7=Dense(1,activation="sigmoid")(c6)

        similarity=c7

        return Model([sample_1,sample_2],similarity)

    # 以下方法与原始版本相同，保持完整的训练逻辑
    def gradient_penalty_loss(self, gradients):
        gradients_sqr = tf.square(gradients)
        gradients_sqr_sum = tf.reduce_sum(gradients_sqr, axis=tf.range(1, len(gradients_sqr.shape)))
        gradient_l2_norm = tf.sqrt(gradients_sqr_sum)
        gradient_penalty = tf.square(1 - gradient_l2_norm)
        return tf.reduce_mean(gradient_penalty)
    
    def wasserstein_loss(self, y_true, y_pred):
        return K.mean(y_true * y_pred)
          
    def estimate_mutual_information(self, x, z):
        z_shuffle = tf.random.shuffle(z)
        
        joint = tf.concat([x, z], axis=-1)
        marginal = tf.concat([x, z_shuffle], axis=-1)
        
        def statistics_network(samples):
            h1 = Dense(64, activation='relu')(samples)
            h2 = Dense(32, activation='relu')(h1)
            return Dense(1)(h2)
        
        t_joint = statistics_network(joint)
        t_marginal = statistics_network(marginal)
        
        mi_est = tf.reduce_mean(t_joint) - tf.math.log(tf.reduce_mean(tf.exp(t_marginal)))
        return mi_est
    
    def mi_penalty_loss(self, x, z):
        mi_est = self.estimate_mutual_information(x, z)
        return tf.maximum(0.0, mi_est - self.mi_bound)  
    
    def classification_loss(self,current_batch_features,y_true, hidden_output, pred_attribute):
        classification_loss = tf.keras.losses.binary_crossentropy(y_true, pred_attribute)
        
        mi_penalty=0    
        if self.bound == True:    
            mi_penalty = self.mi_penalty_loss(current_batch_features, hidden_output)
            
        total_loss = classification_loss + self.mi_weight * mi_penalty
        return total_loss
    
    def comparison_loss(self,feature,similar_feature,unsimilar_feature,similar_truth,unsimilar_truth):
        predicted_similarity_simi=self.m([feature,similar_feature])
        predicted_similarity_unsimi=self.m([feature,unsimilar_feature])
        
        comparison_loss_simi=tf.keras.losses.binary_crossentropy(similar_truth, predicted_similarity_simi)
        comparison_loss_unsimi=tf.keras.losses.binary_crossentropy(unsimilar_truth, predicted_similarity_unsimi)        
        
        total_loss = comparison_loss_simi + comparison_loss_unsimi
        return total_loss
    
    def cycle_rank_loss(self,generated_feature,reconstructed_feature,unsimilar_generated_feature,similar_truth):
        predicted_similarity_simi=self.m([generated_feature,reconstructed_feature])
        predicted_similarity_unsimi=self.m([unsimilar_generated_feature,reconstructed_feature])
        
        comparison_loss_simi=tf.keras.losses.binary_crossentropy(similar_truth, predicted_similarity_simi)
        comparison_loss_unsimi=tf.keras.losses.binary_crossentropy(similar_truth, predicted_similarity_unsimi)
        
        loss = tf.maximum(0, comparison_loss_simi - comparison_loss_unsimi)
        return loss

# 为了保持向后兼容性，创建一个别名
Zero_shot = Zero_shot_with_IF
