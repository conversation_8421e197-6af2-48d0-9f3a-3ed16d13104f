"""
孤立森林过滤器模块
基于论文: "Improved Generative Adversarial Networks With Filtering Mechanism for Fault Data Augmentation"
实现生成样本的质量过滤功能
"""

import numpy as np
import logging
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import tensorflow as tf
from typing import Tuple, Optional, Dict, Any

class IsolationForestFilter:
    """
    孤立森林过滤器类
    用于检测和移除GAN生成的低质量样本
    """
    
    def __init__(self, 
                 contamination: float = 0.1,
                 n_estimators: int = 100,
                 max_samples: str = 'auto',
                 random_state: int = 42,
                 normalize: bool = True):
        """
        初始化孤立森林过滤器
        
        Args:
            contamination: 异常样本比例，论文中使用0.1（10%）
            n_estimators: 孤立树的数量
            max_samples: 每棵树的最大样本数
            random_state: 随机种子
            normalize: 是否对特征进行标准化
        """
        self.contamination = contamination
        self.n_estimators = n_estimators
        self.max_samples = max_samples
        self.random_state = random_state
        self.normalize = normalize
        
        # 初始化孤立森林模型
        self.isolation_forest = IsolationForest(
            contamination=self.contamination,
            n_estimators=self.n_estimators,
            max_samples=self.max_samples,
            random_state=self.random_state,
            n_jobs=-1  # 使用所有CPU核心
        )
        
        # 标准化器（如果需要）
        self.scaler = StandardScaler() if self.normalize else None
        
        # 日志记录器
        self.logger = logging.getLogger('IsolationForestFilter')
        
        # 统计信息
        self.stats = {
            'total_samples_processed': 0,
            'total_samples_removed': 0,
            'removal_rate': 0.0,
            'filter_calls': 0
        }
    
    def fit_filter(self, reference_features: np.ndarray) -> None:
        """
        使用参考特征训练孤立森林模型
        
        Args:
            reference_features: 参考特征（通常是真实样本的特征）
        """
        self.logger.info(f"训练孤立森林过滤器，参考样本数: {reference_features.shape[0]}")
        
        # 标准化（如果需要）
        if self.normalize:
            reference_features = self.scaler.fit_transform(reference_features)
        
        # 训练孤立森林
        self.isolation_forest.fit(reference_features)
        
        self.logger.info("孤立森林过滤器训练完成")
    
    def filter_samples(self,
                      generated_features: np.ndarray,
                      generated_labels: Optional[np.ndarray] = None,
                      min_samples_ratio: float = 0.1) -> Tuple[np.ndarray, Optional[np.ndarray], Dict[str, Any]]:
        """
        过滤生成的样本，移除异常样本
        
        Args:
            generated_features: 生成的特征
            generated_labels: 生成样本对应的标签（可选）
            min_samples_ratio: 最小保留样本比例（防止过度过滤）

        Returns:
            filtered_features: 过滤后的特征
            filtered_labels: 过滤后的标签（如果提供）
            filter_info: 过滤信息统计
        """
        original_count = generated_features.shape[0]
        self.logger.info(f"开始过滤生成样本，原始样本数: {original_count}")
        
        # 标准化（如果需要）
        features_for_prediction = generated_features
        if self.normalize:
            features_for_prediction = self.scaler.transform(generated_features)
        
        # 预测异常样本
        outlier_labels = self.isolation_forest.predict(features_for_prediction)
        
        # 获取异常分数
        anomaly_scores = self.isolation_forest.score_samples(features_for_prediction)
        
        # 保留正常样本（标签为1）
        normal_mask = outlier_labels == 1

        # 检查是否过度过滤，确保至少保留min_samples_ratio比例的样本
        min_samples = max(1, int(original_count * min_samples_ratio))
        if np.sum(normal_mask) < min_samples:
            self.logger.warning(f"过滤过于严格，仅保留 {np.sum(normal_mask)} 个样本，"
                               f"调整为保留质量最好的 {min_samples} 个样本")
            # 根据异常分数选择质量最好的样本
            top_indices = np.argsort(anomaly_scores)[-min_samples:]
            normal_mask = np.zeros(len(anomaly_scores), dtype=bool)
            normal_mask[top_indices] = True

        filtered_features = generated_features[normal_mask]
        filtered_labels = generated_labels[normal_mask] if generated_labels is not None else None
        
        # 统计信息
        filtered_count = filtered_features.shape[0]
        removed_count = original_count - filtered_count
        removal_rate = removed_count / original_count
        
        # 更新全局统计
        self.stats['total_samples_processed'] += original_count
        self.stats['total_samples_removed'] += removed_count
        self.stats['filter_calls'] += 1
        self.stats['removal_rate'] = self.stats['total_samples_removed'] / self.stats['total_samples_processed']
        
        # 过滤信息
        filter_info = {
            'original_count': original_count,
            'filtered_count': filtered_count,
            'removed_count': removed_count,
            'removal_rate': removal_rate,
            'anomaly_scores_mean': np.mean(anomaly_scores),
            'anomaly_scores_std': np.std(anomaly_scores),
            'normal_samples_ratio': np.sum(normal_mask) / len(normal_mask)
        }
        
        self.logger.info(f"过滤完成: {original_count} -> {filtered_count} "
                        f"(移除 {removed_count}, {removal_rate:.2%})")
        
        return filtered_features, filtered_labels, filter_info
    
    def get_anomaly_scores(self, features: np.ndarray) -> np.ndarray:
        """
        获取样本的异常分数
        
        Args:
            features: 输入特征
            
        Returns:
            anomaly_scores: 异常分数数组
        """
        if self.normalize:
            features = self.scaler.transform(features)
        
        return self.isolation_forest.score_samples(features)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取过滤器统计信息
        
        Returns:
            统计信息字典
        """
        return self.stats.copy()
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.stats = {
            'total_samples_processed': 0,
            'total_samples_removed': 0,
            'removal_rate': 0.0,
            'filter_calls': 0
        }
        self.logger.info("统计信息已重置")

def create_isolation_forest_filter(contamination: float = 0.1, 
                                 normalize: bool = True) -> IsolationForestFilter:
    """
    创建孤立森林过滤器的便捷函数
    
    Args:
        contamination: 异常样本比例
        normalize: 是否标准化
        
    Returns:
        配置好的孤立森林过滤器
    """
    return IsolationForestFilter(
        contamination=contamination,
        normalize=normalize
    )

# 用于TensorBoard可视化的辅助函数
def log_filter_metrics_to_tensorboard(writer: tf.summary.SummaryWriter,
                                     filter_info: Dict[str, Any],
                                     step: int) -> None:
    """
    将过滤指标记录到TensorBoard
    
    Args:
        writer: TensorBoard写入器
        filter_info: 过滤信息
        step: 当前步数
    """
    with writer.as_default():
        tf.summary.scalar('Filter/Original_Samples', filter_info['original_count'], step=step)
        tf.summary.scalar('Filter/Filtered_Samples', filter_info['filtered_count'], step=step)
        tf.summary.scalar('Filter/Removed_Samples', filter_info['removed_count'], step=step)
        tf.summary.scalar('Filter/Removal_Rate', filter_info['removal_rate'], step=step)
        tf.summary.scalar('Filter/Anomaly_Scores_Mean', filter_info['anomaly_scores_mean'], step=step)
        tf.summary.scalar('Filter/Anomaly_Scores_Std', filter_info['anomaly_scores_std'], step=step)
        tf.summary.scalar('Filter/Normal_Samples_Ratio', filter_info['normal_samples_ratio'], step=step)
