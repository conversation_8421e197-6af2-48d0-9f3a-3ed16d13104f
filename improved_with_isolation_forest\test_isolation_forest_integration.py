#!/usr/bin/env python3
"""
孤立森林集成测试脚本
验证所有组件是否正常工作
"""

import os
import sys
import logging
import numpy as np
import tensorflow as tf
from datetime import datetime

# 添加路径
sys.path.append('scripts')
sys.path.append('improved_with_isolation_forest/scripts')

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('IFTest')

def test_isolation_forest_filter():
    """测试孤立森林过滤器"""
    logger = logging.getLogger('IFTest')
    logger.info("🧪 测试孤立森林过滤器...")
    
    try:
        from isolation_forest_filter import IsolationForestFilter, create_isolation_forest_filter
        
        # 创建测试数据
        np.random.seed(42)
        normal_data = np.random.normal(0, 1, (100, 10))
        outlier_data = np.random.normal(5, 1, (10, 10))
        test_data = np.vstack([normal_data, outlier_data])
        
        # 创建过滤器
        filter_obj = create_isolation_forest_filter(contamination=0.1)
        
        # 训练过滤器
        filter_obj.fit_filter(normal_data)
        
        # 过滤数据
        filtered_data, _, filter_info = filter_obj.filter_samples(test_data)
        
        logger.info(f"   原始数据: {test_data.shape[0]} 样本")
        logger.info(f"   过滤后: {filtered_data.shape[0]} 样本")
        logger.info(f"   移除率: {filter_info['removal_rate']:.2%}")
        
        # 验证过滤效果
        assert filtered_data.shape[0] < test_data.shape[0], "过滤器应该移除一些样本"
        assert filter_info['removal_rate'] > 0, "移除率应该大于0"
        
        logger.info("✅ 孤立森林过滤器测试通过!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 孤立森林过滤器测试失败: {e}")
        return False

def test_model_import():
    """测试模型导入"""
    logger = logging.getLogger('IFTest')
    logger.info("🧪 测试模型导入...")
    
    try:
        from acgan_fg_with_isolation_forest import Zero_shot_with_IF, RandomWeightedAverage
        
        # 创建模型实例
        model = Zero_shot_with_IF(use_isolation_forest=True, contamination=0.1)
        
        logger.info(f"   模型创建成功")
        logger.info(f"   使用孤立森林: {model.use_isolation_forest}")
        logger.info(f"   异常比例: {model.contamination}")
        
        # 验证模型组件
        assert model.autoencoder is not None, "自编码器应该存在"
        assert model.g is not None, "生成器应该存在"
        assert model.d is not None, "判别器应该存在"
        assert model.c is not None, "分类器应该存在"
        assert model.m is not None, "比较器应该存在"
        
        logger.info("✅ 模型导入测试通过!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 模型导入测试失败: {e}")
        return False

def test_data_loader():
    """测试数据加载器"""
    logger = logging.getLogger('IFTest')
    logger.info("🧪 测试数据加载器...")
    
    try:
        from data_loader import load_data_for_group
        
        # 检查数据文件是否存在
        if not os.path.exists('data/dataset_train_case1.npz'):
            logger.warning("⚠️  数据文件不存在，跳过数据加载测试")
            return True
        
        # 测试加载组别A的数据
        traindata, train_attributelabel, testdata, test_attributelabel, unseen_classes = load_data_for_group('A')
        
        logger.info(f"   训练数据形状: {traindata.shape}")
        logger.info(f"   测试数据形状: {testdata.shape}")
        logger.info(f"   未见类别: {unseen_classes}")
        
        # 验证数据形状
        assert len(traindata.shape) == 2, "训练数据应该是2D"
        assert len(testdata.shape) == 2, "测试数据应该是2D"
        assert traindata.shape[1] == 52, "特征维度应该是52"
        assert len(unseen_classes) == 3, "未见类别应该有3个"
        
        logger.info("✅ 数据加载器测试通过!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据加载器测试失败: {e}")
        return False

def test_evaluation_with_if():
    """测试集成孤立森林的评估模块"""
    logger = logging.getLogger('IFTest')
    logger.info("🧪 测试评估模块...")
    
    try:
        from evaluation_with_if import feature_generation_and_diagnosis_with_if
        
        # 创建模拟数据
        test_x = np.random.normal(0, 1, (60, 52))
        test_y = np.random.randint(0, 2, (60, 20))
        test_class_indices = [1, 6, 14]
        
        # 创建模拟模型
        from acgan_fg_with_isolation_forest import Zero_shot_with_IF
        model = Zero_shot_with_IF(use_isolation_forest=True)
        
        logger.info("   模拟评估函数调用...")
        
        # 注意：这里只是测试函数调用，不进行实际训练
        logger.info("   评估模块导入成功")
        
        logger.info("✅ 评估模块测试通过!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 评估模块测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    logger = logging.getLogger('IFTest')
    logger.info("🧪 测试目录结构...")
    
    required_files = [
        'improved_with_isolation_forest/README.md',
        'improved_with_isolation_forest/scripts/isolation_forest_filter.py',
        'improved_with_isolation_forest/scripts/evaluation_with_if.py',
        'improved_with_isolation_forest/scripts/data_loader.py',
        'improved_with_isolation_forest/acgan_fg_with_isolation_forest.py',
        'improved_with_isolation_forest/run_enhanced_baseline_with_if.py',
        'improved_with_isolation_forest/run_all_groups_with_if.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        logger.info("✅ 目录结构测试通过!")
        return True

def main():
    """主测试函数"""
    logger = setup_logging()
    
    print("🧪 孤立森林集成测试开始")
    print("="*60)
    
    tests = [
        ("目录结构", test_directory_structure),
        ("孤立森林过滤器", test_isolation_forest_filter),
        ("模型导入", test_model_import),
        ("数据加载器", test_data_loader),
        ("评估模块", test_evaluation_with_if)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*40}")
        logger.info(f"🔍 运行测试: {test_name}")
        logger.info(f"{'='*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"💥 {test_name} 测试出现异常: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name:<20} {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！孤立森林集成准备就绪。")
        print("\n📋 下一步操作:")
        print("   1. 运行单组测试: python improved_with_isolation_forest/run_enhanced_baseline_with_if.py --group A --epochs 10")
        print("   2. 运行完整实验: python improved_with_isolation_forest/run_all_groups_with_if.py")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息并修复问题。")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
