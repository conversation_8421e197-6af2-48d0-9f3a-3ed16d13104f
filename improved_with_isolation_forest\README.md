# 孤立森林过滤机制集成版本

## 📁 文件夹结构说明

本文件夹包含了集成孤立森林(Isolation Forest)过滤机制的改进版本，基于论文：
"Improved Generative Adversarial Networks With Filtering Mechanism for Fault Data Augmentation"

## 🎯 改进目标

在现有ACGAN-FG框架中集成孤立森林过滤机制，通过以下方式提升性能：

1. **生成样本质量过滤**：使用孤立森林算法检测并移除低质量的生成样本
2. **异常样本检测**：自动识别偏离正常分布的"垃圾样本"
3. **下游分类器性能提升**：为分类器提供更干净、高质量的训练数据

## 📋 文件说明

### 核心文件
- `acgan_fg_with_isolation_forest.py` - 集成孤立森林的核心模型
- `run_enhanced_baseline_with_if.py` - 带过滤机制的训练脚本
- `run_all_groups_with_if.py` - 批量运行脚本

### 辅助文件
- `scripts/evaluation_with_if.py` - 集成过滤机制的评估模块
- `scripts/isolation_forest_filter.py` - 孤立森林过滤器实现
- `scripts/data_loader.py` - 数据加载器（复制自原版）

## 🔧 主要改进点

### 1. 孤立森林过滤机制
- **位置**：在特征生成后，分类器训练前
- **参数**：contamination=0.1（移除10%异常样本）
- **效果**：提升生成样本质量，减少噪声干扰

### 2. 集成方式
- **无侵入性**：不修改原有GAN架构
- **模块化设计**：可独立开关过滤功能
- **兼容性强**：与现有评估流程完全兼容

## 🚀 使用方法

### 1. 环境准备
确保已安装必要的依赖：
```bash
pip install scikit-learn tensorflow numpy
```

### 2. 测试集成
运行测试脚本验证所有组件：
```bash
python improved_with_isolation_forest/test_isolation_forest_integration.py
```

### 3. 单组训练

#### 使用孤立森林过滤（推荐）
```bash
python improved_with_isolation_forest/run_enhanced_baseline_with_if.py --group A --epochs 200 --batch_size 120 --use_if --contamination 0.1
```

#### 基线对比（不使用过滤）
```bash
python improved_with_isolation_forest/run_enhanced_baseline_with_if.py --group A --epochs 200 --batch_size 120
```

### 4. 批量训练（所有组别对比实验）
```bash
python improved_with_isolation_forest/run_all_groups_with_if.py
```

### 5. 参数说明
- `--group`: 数据组别 (A, B, C, D, E)
- `--epochs`: 训练轮次 (默认200)
- `--batch_size`: 批次大小 (默认120)
- `--use_if`: 启用孤立森林过滤
- `--contamination`: 异常样本比例 (默认0.1，即10%)

## 📊 预期效果

基于论文实验结果，预期可获得：
- **准确率提升**：2-5%
- **训练稳定性**：显著改善
- **样本质量**：明显提升

## 🔍 监控指标

新增监控指标：
- `Filter/Original_Samples` - 原始生成样本数量
- `Filter/Filtered_Samples` - 过滤后样本数量
- `Filter/Removal_Rate` - 样本移除率
- `Quality/Sample_Quality_Score` - 样本质量评分

## 📝 实验记录

实验结果将记录在 `logs/isolation_forest/` 目录下，包含：
- 训练日志
- TensorBoard可视化
- 过滤效果分析
- 性能对比报告
